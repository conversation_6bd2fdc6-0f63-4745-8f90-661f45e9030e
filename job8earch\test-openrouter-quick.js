// Quick test of OpenRouter API
import axios from 'axios';

const API_KEY = '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';
const BASE_URL = 'https://openrouter.ai/api/v1';

async function testOpenRouter() {
  console.log('Testing OpenRouter API...');
  
  try {
    console.log('\n1. Testing /models endpoint...');
    const modelsResponse = await axios.get(`${BASE_URL}/models`, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Models endpoint works!');
    console.log(`Total models: ${modelsResponse.data.data.length}`);
    
    // Find free models
    const freeModels = modelsResponse.data.data.filter(model => 
      model.pricing && 
      model.pricing.prompt === '0' && 
      model.pricing.completion === '0'
    );
    
    console.log(`Free models: ${freeModels.length}`);
    console.log('First 3 free models:');
    freeModels.slice(0, 3).forEach(model => {
      console.log(`  - ${model.name} (${model.id})`);
    });
    
  } catch (error) {
    console.error('❌ Models endpoint failed:', error.response?.status, error.response?.statusText);
    console.error('Error data:', error.response?.data);
  }
  
  // Test chat with the model from .env
  try {
    console.log('\n2. Testing chat with google/gemma-3n-e2b-it:free...');
    
    const chatResponse = await axios.post(`${BASE_URL}/chat/completions`, {
      model: 'google/gemma-3n-e2b-it:free',
      messages: [
        {
          role: 'user',
          content: 'Hello! Just say "API test successful" please.'
        }
      ],
      temperature: 0.7,
      max_tokens: 50
    }, {
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://job8earch.com',
        'X-Title': 'Job8earch'
      }
    });
    
    console.log('✅ Chat endpoint works!');
    console.log('Response:', chatResponse.data.choices[0].message.content);
    
  } catch (error) {
    console.error('❌ Chat endpoint failed:', error.response?.status, error.response?.statusText);
    console.error('Error data:', error.response?.data);
    
    // Try a different model
    try {
      console.log('\n2b. Trying with tencent/hunyuan-a13b-instruct:free...');
      const chatResponse2 = await axios.post(`${BASE_URL}/chat/completions`, {
        model: 'tencent/hunyuan-a13b-instruct:free',
        messages: [
          {
            role: 'user',
            content: 'Hello! Just say "API test successful" please.'
          }
        ],
        temperature: 0.7,
        max_tokens: 50
      }, {
        headers: {
          'Authorization': `Bearer ${API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://job8earch.com',
          'X-Title': 'Job8earch'
        }
      });
      
      console.log('✅ Hunyuan model works!');
      console.log('Response:', chatResponse2.data.choices[0].message.content);
      
    } catch (error2) {
      console.error('❌ Hunyuan model also failed:', error2.response?.status, error2.response?.statusText);
      console.error('Error data:', error2.response?.data);
    }
  }
}

testOpenRouter().catch(console.error);
