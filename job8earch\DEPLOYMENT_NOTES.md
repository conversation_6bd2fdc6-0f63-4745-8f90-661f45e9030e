# Deployment Notes - Job8earch

## CORS Issue with Reed API

### The Problem
The Reed API doesn't allow direct browser requests due to CORS (Cross-Origin Resource Sharing) restrictions. This is a security feature that prevents websites from making unauthorized requests to external APIs.

**Error you might see:**
```
Access to XMLHttpRequest at 'https://www.reed.co.uk/api/1.0/search' from origin 'http://localhost:5174' has been blocked by CORS policy
```

### Current Solution (Development)
- The app currently uses **demo data** with realistic job listings
- All search and filtering functionality works with this demo data
- The AI features work perfectly with OpenRouter API

### Production Solutions

To use real Reed API data in production, you have several options:

#### Option 1: Backend Proxy (Recommended)
Create a backend API that proxies requests to Reed API:

```javascript
// Example Express.js backend
app.get('/api/jobs/search', async (req, res) => {
  try {
    const response = await axios.get('https://www.reed.co.uk/api/1.0/search', {
      params: req.query,
      auth: {
        username: process.env.REED_API_KEY,
        password: ''
      }
    });
    res.json(response.data);
  } catch (error) {
    res.status(500).json({ error: 'Failed to fetch jobs' });
  }
});
```

#### Option 2: Serverless Functions
Use Vercel Functions, Netlify Functions, or similar:

```javascript
// api/jobs.js (Vercel Function)
export default async function handler(req, res) {
  const response = await fetch('https://www.reed.co.uk/api/1.0/search?' + new URLSearchParams(req.query), {
    headers: {
      'Authorization': 'Basic ' + Buffer.from(process.env.REED_API_KEY + ':').toString('base64')
    }
  });
  
  const data = await response.json();
  res.json(data);
}
```

#### Option 3: CORS Proxy Service
Use a CORS proxy service (not recommended for production):
- https://cors-anywhere.herokuapp.com/
- https://api.allorigins.win/

### Current App Status

✅ **Working Features:**
- Job search with demo data (6 realistic job listings)
- Job filtering by keywords and location
- AI chat with OpenRouter models
- Skills analysis and interview preparation
- API connection testing
- Responsive design

🔄 **Demo Mode Features:**
- Search functionality with sample jobs
- Pagination and filtering
- Job details view
- All UI components working

🚀 **Ready for Production:**
- Add backend proxy for Reed API
- Deploy with environment variables
- All other features work perfectly

### Environment Variables for Production

```env
# Required for AI features
VITE_OPENROUTER_API_KEY=your_openrouter_key
VITE_OPENROUTER_MODEL=google/gemma-3n-e2b-it:free

# Required for real job data (when backend proxy is implemented)
VITE_REED_API_KEY=your_reed_api_key

# Optional: Backend API URL (if using separate backend)
VITE_API_BASE_URL=https://your-backend-api.com
```

### Deployment Checklist

- [ ] Set up OpenRouter API key and configure privacy settings
- [ ] Deploy frontend to Vercel/Netlify
- [ ] (Optional) Set up backend proxy for Reed API
- [ ] Configure environment variables
- [ ] Test AI features
- [ ] Test job search functionality

### Notes

- The current demo implementation provides a fully functional job search experience
- Users can test all features including AI chat, skills analysis, and interview prep
- The app is production-ready for AI features
- Reed API integration requires backend implementation for CORS compliance

---

*This app demonstrates a complete job search platform with AI integration. The demo data provides a realistic preview of the full functionality.*
