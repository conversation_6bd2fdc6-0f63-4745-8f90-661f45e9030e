import axios from 'axios';

// Reed API configuration from environment variables
const REED_API_KEY = import.meta.env.VITE_REED_API_KEY;
const REED_BASE_URL = 'https://www.reed.co.uk/api/1.0';

// Create axios instance with Reed API configuration
const reedApi = axios.create({
  baseURL: REED_BASE_URL,
  auth: {
    username: REED_API_KEY,
    password: ''
  },
  headers: {
    'Content-Type': 'application/json',
  }
});

// Enhanced fallback jobs data with realistic job listings
const FALLBACK_JOBS = [
  {
    jobId: 'demo-1',
    jobTitle: 'Senior Software Developer',
    employerName: 'TechCorp Solutions',
    locationName: 'London, UK',
    jobDescription: 'Join our dynamic team as a Senior Software Developer. We are looking for an experienced developer to work on cutting-edge projects using React, Node.js, and cloud technologies. You will be responsible for developing scalable web applications and mentoring junior developers.',
    jobUrl: 'https://www.reed.co.uk/',
    minimumSalary: 60000,
    maximumSalary: 80000,
    fullTime: true,
    partTime: false,
    permanent: true,
    contract: false,
    temp: false,
    date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() // 2 days ago
  },
  {
    jobId: 'demo-2',
    jobTitle: 'Product Manager',
    employerName: 'Innovation Labs',
    locationName: 'Manchester, UK',
    jobDescription: 'We are seeking a Product Manager to drive our product strategy and roadmap. You will work closely with engineering, design, and marketing teams to deliver exceptional user experiences. Experience with agile methodologies and data-driven decision making is essential.',
    jobUrl: 'https://www.reed.co.uk/',
    minimumSalary: 50000,
    maximumSalary: 70000,
    fullTime: true,
    partTime: false,
    permanent: true,
    contract: false,
    temp: false,
    date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString() // 1 day ago
  },
  {
    jobId: 'demo-3',
    jobTitle: 'UX/UI Designer',
    employerName: 'Creative Studio',
    locationName: 'Birmingham, UK',
    jobDescription: 'Looking for a talented UX/UI Designer to create intuitive and beautiful user interfaces. You will be responsible for user research, wireframing, prototyping, and visual design. Experience with Figma, Sketch, and user testing is required.',
    jobUrl: 'https://www.reed.co.uk/',
    minimumSalary: 40000,
    maximumSalary: 55000,
    fullTime: true,
    partTime: false,
    permanent: true,
    contract: false,
    temp: false,
    date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString() // 3 days ago
  },
  {
    jobId: 'demo-4',
    jobTitle: 'Data Scientist',
    employerName: 'Analytics Pro',
    locationName: 'Edinburgh, UK',
    jobDescription: 'Join our data science team to analyze complex datasets and provide actionable insights. You will work with Python, R, SQL, and machine learning frameworks to solve business problems. PhD in a quantitative field preferred.',
    jobUrl: 'https://www.reed.co.uk/',
    minimumSalary: 55000,
    maximumSalary: 75000,
    fullTime: true,
    partTime: false,
    permanent: true,
    contract: false,
    temp: false,
    date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString() // 4 days ago
  },
  {
    jobId: 'demo-5',
    jobTitle: 'Marketing Manager',
    employerName: 'Growth Agency',
    locationName: 'Bristol, UK',
    jobDescription: 'Lead our marketing efforts as a Marketing Manager. You will develop and execute marketing strategies, manage campaigns, and analyze performance metrics. Experience with digital marketing, SEO, and content marketing is essential.',
    jobUrl: 'https://www.reed.co.uk/',
    minimumSalary: 45000,
    maximumSalary: 60000,
    fullTime: true,
    partTime: false,
    permanent: true,
    contract: false,
    temp: false,
    date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString() // 5 days ago
  },
  {
    jobId: 'demo-6',
    jobTitle: 'DevOps Engineer',
    employerName: 'CloudTech Systems',
    locationName: 'Leeds, UK',
    jobDescription: 'We are looking for a DevOps Engineer to manage our cloud infrastructure and CI/CD pipelines. Experience with AWS, Docker, Kubernetes, and Infrastructure as Code is required. You will work on automating deployments and ensuring system reliability.',
    jobUrl: 'https://www.reed.co.uk/',
    minimumSalary: 50000,
    maximumSalary: 70000,
    fullTime: true,
    partTime: false,
    permanent: true,
    contract: false,
    temp: false,
    date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString() // 6 days ago
  }
];

/**
 * Search for jobs using Reed API
 * @param {Object} params - Search parameters
 * @param {string} params.keywords - Job keywords to search for
 * @param {string} params.locationName - Location to search in
 * @param {number} params.resultsToTake - Number of results to return
 * @param {number} params.resultsToSkip - Number of results to skip for pagination
 * @returns {Promise} API response with job listings
 */
export const searchJobs = async (params = {}) => {
  try {
    // Check if API key is configured
    if (!REED_API_KEY) {
      throw new Error('Reed API key is not configured');
    }

    const { keywords = '', locationName = '', resultsToTake = 20, resultsToSkip = 0 } = params;

    // Build query parameters
    const queryParams = new URLSearchParams();

    if (keywords) queryParams.append('keywords', keywords);
    if (locationName) queryParams.append('locationName', locationName);
    queryParams.append('resultsToTake', resultsToTake.toString());
    queryParams.append('resultsToSkip', resultsToSkip.toString());

    // Make API request to Reed
    const response = await reedApi.get(`/search?${queryParams.toString()}`);

    if (response.data && response.data.results) {
      return {
        success: true,
        data: response.data,
        totalResults: response.data.totalResults || response.data.results.length,
        jobs: response.data.results
      };
    } else {
      throw new Error('Invalid response format from Reed API');
    }

  } catch (error) {
    console.error('Reed API Error:', error);

    // Return fallback data when API fails (like CORS in development)
    return {
      success: false,
      error: error.message.includes('CORS') || error.message.includes('Network Error')
        ? 'CORS error in development - this will work in production. Showing sample data.'
        : error.response?.data?.message || error.message || 'Failed to fetch jobs from Reed API',
      jobs: FALLBACK_JOBS,
      totalResults: FALLBACK_JOBS.length,
      data: { results: FALLBACK_JOBS, totalResults: FALLBACK_JOBS.length },
      fallback: true
    };
  }
};

/**
 * Get job details by ID - Uses fallback data due to CORS restrictions
 * @param {string} jobId - Job ID
 * @returns {Promise} Job details
 */
export const getJobDetails = async (jobId) => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Find job in fallback data
    const job = FALLBACK_JOBS.find(j => j.jobId === jobId);

    if (job) {
      return {
        success: true,
        data: job,
        isDemo: true
      };
    } else {
      return {
        success: false,
        error: 'Job not found. This may be because the job has been removed or the ID is incorrect.'
      };
    }

  } catch (error) {
    console.error('Job Details Error:', error);

    return {
      success: false,
      error: 'Failed to fetch job details. Please try again.'
    };
  }
};

/**
 * Get popular job categories and locations
 * This is a helper function to provide common search options
 */
export const getJobCategories = () => {
  return [
    'Software Developer',
    'Data Scientist',
    'Product Manager',
    'UX Designer',
    'Marketing Manager',
    'Sales Executive',
    'Business Analyst',
    'Project Manager',
    'DevOps Engineer',
    'Frontend Developer',
    'Backend Developer',
    'Full Stack Developer',
    'Mobile Developer',
    'QA Engineer',
    'Cybersecurity Specialist'
  ];
};

export const getPopularLocations = () => {
  return [
    'London',
    'Manchester',
    'Birmingham',
    'Leeds',
    'Glasgow',
    'Bristol',
    'Edinburgh',
    'Liverpool',
    'Newcastle',
    'Sheffield',
    'Remote',
    'Hybrid'
  ];
};

/**
 * Test Reed API connection
 * @returns {Promise} API connection test result
 */
export const testReedApiConnection = async () => {
  try {
    if (!REED_API_KEY) {
      return {
        success: false,
        error: 'Reed API key is not configured. Please add VITE_REED_API_KEY to your environment variables.'
      };
    }

    // Test with a simple search
    const response = await reedApi.get('/search?keywords=developer&resultsToTake=1');

    return {
      success: true,
      message: 'Reed API connection successful',
      totalJobs: response.data?.totalResults || 0
    };

  } catch (error) {
    return {
      success: false,
      error: error.message.includes('CORS') || error.message.includes('Network Error')
        ? 'CORS error in development - API will work in production'
        : error.response?.data?.message || error.message || 'Failed to connect to Reed API'
    };
  }
};

/**
 * Format salary for display
 * @param {number} min - Minimum salary
 * @param {number} max - Maximum salary
 * @returns {string} Formatted salary string
 */
export const formatSalary = (min, max) => {
  if (!min && !max) return 'Salary not specified';
  if (min && max) return `£${min.toLocaleString()} - £${max.toLocaleString()}`;
  if (min) return `From £${min.toLocaleString()}`;
  if (max) return `Up to £${max.toLocaleString()}`;
};

/**
 * Format job type for display
 * @param {Object} job - Job object from Reed API
 * @returns {string} Formatted job type
 */
export const formatJobType = (job) => {
  const types = [];
  if (job.fullTime) types.push('Full-time');
  if (job.partTime) types.push('Part-time');
  if (job.permanent) types.push('Permanent');
  if (job.contract) types.push('Contract');
  if (job.temp) types.push('Temporary');
  
  return types.length > 0 ? types.join(', ') : 'Not specified';
};
